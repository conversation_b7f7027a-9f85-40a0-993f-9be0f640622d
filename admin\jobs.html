<!doctype html>
<html lang="en">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Ed-admin Job Management - Admin Panel">
        <meta name="robots" content="noindex, nofollow">

        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="../assets/css/boxicons.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="../assets/css/style.css">

        <title>Ed-admin: Job Management - Admin Panel</title>
        <link rel="icon" type="image/png" href="../assets/img/favicon-Edadmin.ico">

        <!-- Custom Admin CSS -->
        <style>
            body {
                background-color: #ffffff;
                font-family: 'Poppins', sans-serif;
                color: #333333;
            }

            .admin-header {
                background: linear-gradient(135deg, #006EB3 0%, #0056a3 100%);
                color: white;
                padding: 20px 0;
                margin-bottom: 30px;
                box-shadow: 0 4px 15px rgba(0, 107, 179, 0.15);
            }

            .admin-card {
                background: white;
                border-radius: 15px;
                padding: 30px;
                margin-bottom: 30px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
                border: 1px solid #f0f0f0;
                border-top: 3px solid #FF9300;
            }

            .btn-primary {
                background: #006EB3;
                border: none;
                border-radius: 5px;
                padding: 13px 25px;
                font-weight: 500;
                font-size: 15px;
                transition: all 0.5s ease;
                position: relative;
                overflow: hidden;
                z-index: 1;
            }

            .btn-primary::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 0;
                height: 100%;
                background: #FF9300;
                transition: width 0.5s ease;
                z-index: -1;
            }

            .btn-primary:hover {
                background: #006EB3;
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 107, 179, 0.3);
            }

            .btn-primary:hover::before {
                width: 100%;
            }

            .btn-danger {
                background: #dc3545;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: 500;
                transition: all 0.3s ease;
            }

            .btn-danger:hover {
                background: #c82333;
                transform: translateY(-1px);
            }

            .btn-warning {
                background: #FF9300;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: 500;
                color: white;
                transition: all 0.3s ease;
            }

            .btn-warning:hover {
                background: #e6840a;
                color: white;
                transform: translateY(-1px);
            }

            .btn-secondary {
                background: #4a6f8a;
                border: none;
                border-radius: 5px;
                padding: 13px 25px;
                font-weight: 500;
                color: white;
                transition: all 0.3s ease;
            }

            .btn-secondary:hover {
                background: #3a5a75;
                color: white;
            }

            .btn-light {
                background: white;
                border: 2px solid #FF9300;
                border-radius: 5px;
                padding: 11px 23px;
                font-weight: 500;
                color: #FF9300;
                transition: all 0.3s ease;
            }

            .btn-light:hover {
                background: #FF9300;
                color: white;
            }

            .form-control {
                border-radius: 5px;
                border: 2px solid #e9ecef;
                padding: 12px 15px;
                transition: all 0.3s ease;
                font-family: 'Poppins', sans-serif;
            }

            .form-control:focus {
                border-color: #006EB3;
                box-shadow: 0 0 0 0.2rem rgba(0, 107, 179, 0.25);
            }

            .job-item {
                background: white;
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 2px 10px rgba(8, 10, 60, 0.1);
                transition: all 0.3s ease;
                border-left: 4px solid #FF9300;
            }

            .job-item:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(8, 10, 60, 0.15);
                border-left-color: #006EB3;
            }

            .job-meta {
                display: flex;
                gap: 10px;
                margin-bottom: 10px;
            }

            .job-badge {
                background: #e8f4fd;
                color: #006EB3;
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 12px;
                font-weight: 500;
            }

            .loading-spinner {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #006EB3;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .alert {
                border-radius: 10px;
                border: none;
                padding: 15px 20px;
            }

            .alert-success {
                background-color: #d4edda;
                color: #155724;
                border-left: 4px solid #28a745;
            }

            .alert-danger {
                background-color: #f8d7da;
                color: #721c24;
                border-left: 4px solid #dc3545;
            }

            .alert-warning {
                background-color: #fff3cd;
                color: #856404;
                border-left: 4px solid #FF9300;
            }

            .modal-content {
                border-radius: 15px;
                border: none;
            }

            .modal-header {
                background: linear-gradient(135deg, #006EB3 0%, #080a3c 100%);
                color: white;
                border-radius: 15px 15px 0 0;
            }

            .stats-card {
                background: linear-gradient(135deg, #006EB3 0%, #080a3c 100%);
                color: white;
                border-radius: 15px;
                padding: 25px;
                text-align: center;
                margin-bottom: 20px;
                box-shadow: 0 4px 15px rgba(0, 107, 179, 0.2);
            }

            .stats-card:nth-child(2) {
                background: linear-gradient(135deg, #FF9300 0%, #e6840a 100%);
            }

            .stats-card:nth-child(3) {
                background: linear-gradient(135deg, #4a6f8a 0%, #3a5a75 100%);
            }

            .stats-number {
                font-size: 2.5rem;
                font-weight: 700;
                margin-bottom: 5px;
            }

            .stats-label {
                font-size: 1rem;
                opacity: 0.9;
            }

            h1, h2, h3, h4, h5, h6 {
                color: #080a3c;
                font-weight: 600;
            }

            label {
                color: #080a3c;
                font-weight: 500;
                margin-bottom: 8px;
            }

            .text-muted {
                color: #4a6f8a !important;
            }

            /* Ed-admin style section titles */
            .section-title h3 {
                position: relative;
                display: inline-block;
            }

            .section-title h3::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 0;
                width: 50px;
                height: 3px;
                background: #FF9300;
            }
        </style>
    </head>

    <body>
        <!-- Admin Header -->
        <div class="admin-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 style="margin: 0; font-weight: 600; color: white;">
                            <i class='bx bx-briefcase' style="color: white;"></i> Job Management
                        </h1>
                        <p style="margin: 5px 0 0 0; opacity: 0.9; color: white;">Manage job listings for Ed-admin careers page</p>
                    </div>
                    <div class="col-md-6 text-right">
                        <span id="admin-info" style="color: white; margin-right: 15px; opacity: 0.9;">
                            <i class='bx bx-user'></i> <span id="admin-username">Admin</span>
                        </span>
                        <a href="../join-our-team.html" class="btn btn-light mr-2" target="_blank">
                            <i class='bx bx-external-link'></i> View Public Page
                        </a>
                        <button class="btn btn-secondary" onclick="logout()">
                            <i class='bx bx-log-out'></i> Logout
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <!-- Navigation Tabs -->
            <div class="row mb-4">
                <div class="col-12">
                    <ul class="nav nav-tabs" style="border-bottom: 2px solid #e9ecef;">
                        <li class="nav-item">
                            <a class="nav-link active" id="jobs-tab" href="#" onclick="showSection('jobs')" style="color: #006EB3; font-weight: 600;">
                                <i class='bx bx-briefcase'></i> Job Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="users-tab" href="#" onclick="showSection('users')" style="color: #4a6f8a; font-weight: 600;">
                                <i class='bx bx-users'></i> User Management
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Jobs Management Section -->
            <div id="jobs-section">
                <!-- Stats Section -->
                <div class="row">
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number" id="total-jobs">0</div>
                        <div class="stats-label">Total Jobs</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #FF9300 0%, #e6840a 100%);">
                        <div class="stats-number" id="active-jobs">0</div>
                        <div class="stats-label">Active Listings</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #4a6f8a 0%, #3a5a75 100%);">
                        <div class="stats-number" id="departments">0</div>
                        <div class="stats-label">Departments</div>
                    </div>
                </div>
            </div>

            <!-- Add New Job Section -->
            <div class="admin-card">
                <div class="section-title">
                    <h3 style="margin-bottom: 25px;">
                        <i class='bx bx-plus-circle'></i> Add New Job
                    </h3>
                </div>

                <form id="job-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="job-title">Job Title *</label>
                                <input type="text" class="form-control" id="job-title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="job-department">Department *</label>
                                <select class="form-control" id="job-department" required>
                                    <option value="">Select Department</option>
                                    <option value="Engineering">Engineering</option>
                                    <option value="Product">Product</option>
                                    <option value="Design">Design</option>
                                    <option value="Marketing">Marketing</option>
                                    <option value="Sales">Sales</option>
                                    <option value="Customer Success">Customer Success</option>
                                    <option value="HR">Human Resources</option>
                                    <option value="Finance">Finance</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="job-location">Location *</label>
                                <input type="text" class="form-control" id="job-location" placeholder="e.g., Remote / San Francisco" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="job-type">Job Type *</label>
                                <select class="form-control" id="job-type" required>
                                    <option value="">Select Type</option>
                                    <option value="Full-time">Full-time</option>
                                    <option value="Part-time">Part-time</option>
                                    <option value="Contract">Contract</option>
                                    <option value="Internship">Internship</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="job-description">Job Description *</label>
                        <textarea class="form-control" id="job-description" rows="4" placeholder="Describe the role, responsibilities, and what makes this position exciting..." required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="job-benefits">Benefits *</label>
                        <textarea class="form-control" id="job-benefits" rows="3" placeholder="Enter each benefit on a new line. Example:&#10;Competitive salary&#10;Health insurance&#10;Flexible hours" required></textarea>
                        <small class="form-text text-muted">Enter each benefit on a separate line</small>
                    </div>

                    <div class="form-group">
                        <label for="job-what-youll-do">What You'll Do *</label>
                        <textarea class="form-control" id="job-what-youll-do" rows="3" placeholder="Enter each responsibility on a new line. Example:&#10;Design applications&#10;Lead team meetings&#10;Review code" required></textarea>
                        <small class="form-text text-muted">Enter each responsibility on a separate line</small>
                    </div>

                    <div class="form-group">
                        <label for="job-what-youll-need">What You'll Need *</label>
                        <textarea class="form-control" id="job-what-youll-need" rows="3" placeholder="Enter each requirement on a new line. Example:&#10;5+ years experience&#10;Bachelor's degree&#10;Strong communication skills" required></textarea>
                        <small class="form-text text-muted">Enter each requirement on a separate line</small>
                    </div>

                    <div class="form-group">
                        <label for="job-requirements">Additional Requirements *</label>
                        <textarea class="form-control" id="job-requirements" rows="3" placeholder="Any additional requirements, certifications, or preferences..." required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="job-image">Image URL (Optional)</label>
                        <input type="url" class="form-control" id="job-image" placeholder="https://images.unsplash.com/...">
                        <small class="form-text text-muted">Leave empty to use default image</small>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <span id="submit-text">
                            <i class='bx bx-plus'></i> Add Job
                        </span>
                        <span id="submit-loading" style="display: none;">
                            <span class="loading-spinner"></span> Adding...
                        </span>
                    </button>
                    <button type="button" class="btn btn-secondary ml-2" onclick="resetForm()">
                        <i class='bx bx-refresh'></i> Reset
                    </button>
                </form>
            </div>

            <!-- Jobs List Section -->
            <div class="admin-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="section-title">
                        <h3 style="margin: 0;">
                            <i class='bx bx-list-ul'></i> Current Job Listings
                        </h3>
                    </div>
                    <button class="btn btn-primary" onclick="loadJobs()">
                        <i class='bx bx-refresh'></i> Refresh
                    </button>
                </div>

                <!-- Loading State -->
                <div id="jobs-loading" style="text-align: center; padding: 40px;">
                    <div class="loading-spinner" style="width: 40px; height: 40px;"></div>
                    <p style="margin-top: 15px; color: #666;">Loading jobs...</p>
                </div>

                <!-- Error State -->
                <div id="jobs-error" style="display: none;">
                    <div class="alert alert-danger">
                        <h5><i class='bx bx-error'></i> Error Loading Jobs</h5>
                        <p>Unable to load job listings. Please check if the backend server is running.</p>
                    </div>
                </div>

                <!-- Jobs Container -->
                <div id="jobs-container">
                    <!-- Jobs will be loaded here -->
                </div>

                <!-- Empty State -->
                <div id="jobs-empty" style="display: none; text-align: center; padding: 40px;">
                    <i class='bx bx-briefcase' style="font-size: 4rem; color: #ccc; margin-bottom: 20px;"></i>
                    <h4 style="color: #666;">No Jobs Found</h4>
                    <p style="color: #999;">Start by adding your first job listing above.</p>
                </div>
            </div>
            </div>

            <!-- User Management Section -->
            <div id="users-section" style="display: none;">
            <div class="admin-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="section-title">
                        <h3 style="margin: 0;">
                            <i class='bx bx-users'></i> User Management
                        </h3>
                    </div>
                    <button class="btn btn-primary" onclick="showAddUserModal()">
                        <i class='bx bx-user-plus'></i> Add New User
                    </button>
                </div>

                <!-- Users Loading State -->
                <div id="users-loading" style="text-align: center; padding: 40px; display: none;">
                    <div class="loading-spinner" style="width: 40px; height: 40px;"></div>
                    <p style="margin-top: 15px; color: #666;">Loading users...</p>
                </div>

                <!-- Users Error State -->
                <div id="users-error" style="display: none;">
                    <div class="alert alert-danger">
                        <h5><i class='bx bx-error'></i> Error Loading Users</h5>
                        <p>Unable to load user list. Please check if the backend server is running.</p>
                    </div>
                </div>

                <!-- Users Container -->
                <div id="users-container">
                    <!-- Users will be loaded here -->
                </div>

                <!-- Users Empty State -->
                <div id="users-empty" style="display: none; text-align: center; padding: 40px;">
                    <i class='bx bx-users' style="font-size: 4rem; color: #ccc; margin-bottom: 20px;"></i>
                    <h4 style="color: #666;">No Users Found</h4>
                    <p style="color: #999;">Start by adding your first admin user above.</p>
                </div>
            </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div id="message-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>

        <!-- Edit Job Modal -->
        <div class="modal fade" id="editJobModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class='bx bx-edit'></i> Edit Job
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="edit-job-form">
                            <input type="hidden" id="edit-job-id">
                            <!-- Form fields will be populated by JavaScript -->
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="updateJob()">
                            <span id="update-text">Update Job</span>
                            <span id="update-loading" style="display: none;">
                                <span class="loading-spinner"></span> Updating...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add User Modal -->
        <div class="modal fade" id="addUserModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class='bx bx-user-plus'></i> Add New Admin User
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="add-user-form">
                            <div class="form-group">
                                <label for="new-username">Username *</label>
                                <input type="text" class="form-control" id="new-username" required>
                                <small class="form-text text-muted">Username must be unique</small>
                            </div>
                            <div class="form-group">
                                <label for="new-password">Password *</label>
                                <input type="password" class="form-control" id="new-password" required>
                                <small class="form-text text-muted">Minimum 6 characters recommended</small>
                            </div>
                            <div class="form-group">
                                <label for="new-email">Email (Optional)</label>
                                <input type="email" class="form-control" id="new-email">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="createUser()">
                            <span id="create-user-text">Create User</span>
                            <span id="create-user-loading" style="display: none;">
                                <span class="loading-spinner"></span> Creating...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Change Password Modal -->
        <div class="modal fade" id="changePasswordModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class='bx bx-key'></i> Change Password
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="change-password-form">
                            <div class="form-group">
                                <label for="current-password">Current Password *</label>
                                <input type="password" class="form-control" id="current-password" required>
                            </div>
                            <div class="form-group">
                                <label for="new-password">New Password *</label>
                                <input type="password" class="form-control" id="new-password" required>
                                <small class="form-text text-muted">Minimum 6 characters recommended</small>
                            </div>
                            <div class="form-group">
                                <label for="confirm-password">Confirm New Password *</label>
                                <input type="password" class="form-control" id="confirm-password" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="changePassword()">
                            <span id="change-password-text">Change Password</span>
                            <span id="change-password-loading" style="display: none;">
                                <span class="loading-spinner"></span> Changing...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scripts -->
        <script src="../assets/js/jquery.min.js"></script>
        <script src="../assets/js/bootstrap.min.js"></script>

        <!-- Admin JavaScript -->
        <script>
            // API Configuration
            const API_BASE_URL = 'https://job-post-backend-zoku.onrender.com/api';
            const ADMIN_API_URL = `${API_BASE_URL}/admin/jobs`;
            const JOBS_API_URL = `${API_BASE_URL}/jobs`;
            const USERS_API_URL = `${API_BASE_URL}/admin/users`;

            // Global variables
            let currentEditingJobId = null;
            let authCheckAttempts = 0;
            const MAX_AUTH_ATTEMPTS = 3;

            // Initialize page
            document.addEventListener('DOMContentLoaded', function() {
                // Add a longer delay to ensure session is properly established
                setTimeout(() => {
                    checkAuthenticationWithRetry();
                }, 500); // Increased delay to 500ms
                setupEventListeners();
            });

            // Check authentication with retry mechanism
            async function checkAuthenticationWithRetry() {
                authCheckAttempts++;
                console.log(`🔄 Authentication attempt ${authCheckAttempts}/${MAX_AUTH_ATTEMPTS}`);

                try {
                    const success = await checkAuthentication();
                    if (success === true) {
                        console.log('✅ Authentication successful, stopping retry attempts');
                        return;
                    }
                } catch (error) {
                    console.error(`❌ Authentication attempt ${authCheckAttempts} failed:`, error);
                }

                // If we get here, authentication failed
                if (authCheckAttempts < MAX_AUTH_ATTEMPTS) {
                    console.log(`⏳ Retrying authentication in 1 second... (attempt ${authCheckAttempts + 1}/${MAX_AUTH_ATTEMPTS})`);
                    setTimeout(() => {
                        checkAuthenticationWithRetry();
                    }, 1000);
                } else {
                    console.error('❌ All authentication attempts failed, redirecting to login');
                    redirectToLogin('Authentication failed after multiple attempts. Please login again.');
                }
            }

            // Token Management Class
            class AuthManager {
                static getToken() {
                    return localStorage.getItem('edAdminToken');
                }

                static getUser() {
                    const user = localStorage.getItem('edAdminUser');
                    return user ? JSON.parse(user) : null;
                }

                static clearAuth() {
                    localStorage.removeItem('edAdminToken');
                    localStorage.removeItem('edAdminUser');
                    console.log('Auth data cleared from localStorage');
                }

                static isAuthenticated() {
                    return !!this.getToken();
                }

                static getAuthHeaders() {
                    const token = this.getToken();
                    return token ? { 'Authorization': `Bearer ${token}` } : {};
                }
            }

            // Check authentication status
            async function checkAuthentication() {
                try {
                    console.log('=== AUTHENTICATION CHECK START ===');
                    console.log('Current URL:', window.location.href);

                    if (!AuthManager.isAuthenticated()) {
                        console.log('No auth token found, redirecting to login...');
                        redirectToLogin('Please login first to access the admin panel.');
                        return false;
                    }

                    console.log('Auth token found, verifying with server...');
                    console.log('Making request to:', `${API_BASE_URL}/admin/verify-token`);

                    // Verify token with server
                    const response = await fetch(`${API_BASE_URL}/admin/verify-token`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...AuthManager.getAuthHeaders()
                        }
                    });

                    console.log('Auth check response status:', response.status);

                    if (response.ok) {
                        const result = await response.json();
                        console.log('Auth check result:', result);

                        if (result.valid) {
                            // User is authenticated, update UI and load data
                            console.log('✅ User authenticated successfully, loading data...');

                            // Get user info from localStorage or response
                            const user = result.user || AuthManager.getUser();
                            if (user) {
                                document.getElementById('admin-username').textContent = user.username;
                            }

                            // Reset auth attempts counter on success
                            authCheckAttempts = 0;

                            loadJobs();
                            loadUsers();
                            console.log('=== AUTHENTICATION CHECK SUCCESS ===');
                            return true; // Indicate success
                        }
                    }

                    // Token is invalid or expired
                    const errorResult = await response.json().catch(() => ({ message: 'Authentication failed' }));
                    console.log('❌ Authentication failed:', errorResult.message);
                    console.log('=== AUTHENTICATION CHECK FAILED ===');

                    // Clear invalid auth data
                    AuthManager.clearAuth();

                    // Don't redirect immediately if we're in retry mode
                    if (authCheckAttempts >= MAX_AUTH_ATTEMPTS) {
                        redirectToLogin(errorResult.message || 'Your session has expired. Please login again.');
                    }
                    return false;

                } catch (error) {
                    console.error('❌ Authentication check failed with error:', error);
                    console.log('=== AUTHENTICATION CHECK ERROR ===');

                    // Clear auth data on error
                    AuthManager.clearAuth();

                    // Don't redirect immediately if we're in retry mode
                    if (authCheckAttempts >= MAX_AUTH_ATTEMPTS) {
                        redirectToLogin('Unable to verify authentication. Please login again.');
                    }
                    return false;
                }
            }

            // Redirect to login with message
            function redirectToLogin(message) {
                console.log('🔄 Redirecting to login with message:', message);
                // Store message in sessionStorage to show on login page
                sessionStorage.setItem('loginMessage', message);

                // Add a small delay to ensure logging is complete
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 100);
            }

            // Logout function
            async function logout() {
                if (!confirm('Are you sure you want to logout?')) {
                    return;
                }

                try {
                    console.log('Logging out...');

                    // Clear authentication data
                    AuthManager.clearAuth();

                    // Also call logout endpoint if available
                    const response = await fetch(`${API_BASE_URL}/admin/logout`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...AuthManager.getAuthHeaders()
                        }
                    });

                    console.log('Logout response:', response.status);

                } catch (error) {
                    console.error('Logout error:', error);
                }

                // Always redirect to login after logout attempt
                console.log('Redirecting to login page...');
                window.location.href = 'index.html';
            }

            // Setup event listeners
            function setupEventListeners() {
                document.getElementById('job-form').addEventListener('submit', handleAddJob);
            }

            // Load and display jobs
            async function loadJobs() {
                try {
                    showJobsLoading();
                    hideJobsError();
                    hideJobsEmpty();

                    const response = await fetch(JOBS_API_URL);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const jobs = await response.json();

                    hideJobsLoading();

                    if (jobs.length === 0) {
                        showJobsEmpty();
                    } else {
                        displayJobs(jobs);
                    }

                    updateStats(jobs);

                } catch (error) {
                    console.error('Error loading jobs:', error);
                    hideJobsLoading();
                    showJobsError();
                }
            }

            // Display jobs in the list
            function displayJobs(jobs) {
                const container = document.getElementById('jobs-container');
                container.innerHTML = '';

                jobs.forEach(job => {
                    const jobElement = createJobElement(job);
                    container.appendChild(jobElement);
                });
            }

            // Create job element
            function createJobElement(job) {
                const jobDiv = document.createElement('div');
                jobDiv.className = 'job-item';
                jobDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="job-meta">
                                <span class="job-badge">${job.department}</span>
                                <span class="job-badge" style="background: #fff3e0; color: #FF9300;">${job.type}</span>
                            </div>
                            <h5 style="color: #080a3c; margin-bottom: 8px;">${job.title}</h5>
                            <p style="color: #4a6f8a; margin-bottom: 10px;">
                                <i class='bx bx-map'></i> ${job.location}
                            </p>
                            <p style="color: #4a6f8a; margin-bottom: 15px; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                                ${job.description}
                            </p>
                            <small style="color: #4a6f8a;">
                                <i class='bx bx-time'></i> Created: ${new Date(job.created_at).toLocaleDateString()}
                            </small>
                        </div>
                        <div class="ml-3">
                            <button class="btn btn-warning btn-sm mb-2" onclick="editJob(${job.id})" title="Edit Job">
                                <i class='bx bx-edit'></i>
                            </button>
                            <br>
                            <button class="btn btn-danger btn-sm" onclick="deleteJob(${job.id}, '${job.title.replace(/'/g, "\\'")}')">
                                <i class='bx bx-trash'></i>
                            </button>
                        </div>
                    </div>
                `;

                return jobDiv;
            }

            // Handle add job form submission
            async function handleAddJob(event) {
                event.preventDefault();

                const formData = {
                    title: document.getElementById('job-title').value.trim(),
                    department: document.getElementById('job-department').value,
                    location: document.getElementById('job-location').value.trim(),
                    type: document.getElementById('job-type').value,
                    description: document.getElementById('job-description').value.trim(),
                    benefits: document.getElementById('job-benefits').value.trim(),
                    what_youll_do: document.getElementById('job-what-youll-do').value.trim(),
                    what_youll_need: document.getElementById('job-what-youll-need').value.trim(),
                    requirements: document.getElementById('job-requirements').value.trim(),
                    image_url: document.getElementById('job-image').value.trim()
                };

                // Validation
                if (!formData.title || !formData.department || !formData.location ||
                    !formData.type || !formData.description || !formData.benefits ||
                    !formData.what_youll_do || !formData.what_youll_need || !formData.requirements) {
                    showMessage('Please fill in all required fields.', 'danger');
                    return;
                }

                try {
                    showSubmitLoading();

                    const response = await fetch(ADMIN_API_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        credentials: 'include',
                        body: JSON.stringify(formData)
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to create job');
                    }

                    showMessage('Job created successfully!', 'success');
                    resetForm();
                    loadJobs(); // Reload the jobs list

                } catch (error) {
                    console.error('Error creating job:', error);
                    showMessage('Error creating job: ' + error.message, 'danger');
                } finally {
                    hideSubmitLoading();
                }
            }

            // Edit job
            async function editJob(jobId) {
                try {
                    const response = await fetch(`${JOBS_API_URL}/${jobId}`);

                    if (!response.ok) {
                        throw new Error('Failed to fetch job details');
                    }

                    const job = await response.json();
                    currentEditingJobId = jobId;

                    // Populate edit form
                    populateEditForm(job);

                    // Show modal
                    $('#editJobModal').modal('show');

                } catch (error) {
                    console.error('Error fetching job:', error);
                    showMessage('Error loading job details: ' + error.message, 'danger');
                }
            }

            // Convert bullet-separated text to line-separated text for editing
            function convertBulletsToLines(text) {
                if (!text) return '';
                // Convert bullet symbols to new lines for editing
                return text.replace(/•/g, '\n').trim();
            }

            // Populate edit form
            function populateEditForm(job) {
                const modalBody = document.querySelector('#editJobModal .modal-body form');
                modalBody.innerHTML = `
                    <input type="hidden" id="edit-job-id" value="${job.id}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Job Title *</label>
                                <input type="text" class="form-control" id="edit-job-title" value="${job.title}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Department *</label>
                                <select class="form-control" id="edit-job-department" required>
                                    <option value="Engineering" ${job.department === 'Engineering' ? 'selected' : ''}>Engineering</option>
                                    <option value="Product" ${job.department === 'Product' ? 'selected' : ''}>Product</option>
                                    <option value="Design" ${job.department === 'Design' ? 'selected' : ''}>Design</option>
                                    <option value="Marketing" ${job.department === 'Marketing' ? 'selected' : ''}>Marketing</option>
                                    <option value="Sales" ${job.department === 'Sales' ? 'selected' : ''}>Sales</option>
                                    <option value="Customer Success" ${job.department === 'Customer Success' ? 'selected' : ''}>Customer Success</option>
                                    <option value="HR" ${job.department === 'HR' ? 'selected' : ''}>Human Resources</option>
                                    <option value="Finance" ${job.department === 'Finance' ? 'selected' : ''}>Finance</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Location *</label>
                                <input type="text" class="form-control" id="edit-job-location" value="${job.location}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Job Type *</label>
                                <select class="form-control" id="edit-job-type" required>
                                    <option value="Full-time" ${job.type === 'Full-time' ? 'selected' : ''}>Full-time</option>
                                    <option value="Part-time" ${job.type === 'Part-time' ? 'selected' : ''}>Part-time</option>
                                    <option value="Contract" ${job.type === 'Contract' ? 'selected' : ''}>Contract</option>
                                    <option value="Internship" ${job.type === 'Internship' ? 'selected' : ''}>Internship</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Job Description *</label>
                        <textarea class="form-control" id="edit-job-description" rows="4" required>${job.description}</textarea>
                    </div>
                    <div class="form-group">
                        <label>Benefits *</label>
                        <textarea class="form-control" id="edit-job-benefits" rows="3" required>${convertBulletsToLines(job.benefits || '')}</textarea>
                        <small class="form-text text-muted">Enter each benefit on a separate line</small>
                    </div>
                    <div class="form-group">
                        <label>What You'll Do *</label>
                        <textarea class="form-control" id="edit-job-what-youll-do" rows="3" required>${convertBulletsToLines(job.what_youll_do || '')}</textarea>
                        <small class="form-text text-muted">Enter each responsibility on a separate line</small>
                    </div>
                    <div class="form-group">
                        <label>What You'll Need *</label>
                        <textarea class="form-control" id="edit-job-what-youll-need" rows="3" required>${convertBulletsToLines(job.what_youll_need || '')}</textarea>
                        <small class="form-text text-muted">Enter each requirement on a separate line</small>
                    </div>
                    <div class="form-group">
                        <label>Additional Requirements *</label>
                        <textarea class="form-control" id="edit-job-requirements" rows="3" required>${job.requirements}</textarea>
                    </div>
                    <div class="form-group">
                        <label>Image URL</label>
                        <input type="url" class="form-control" id="edit-job-image" value="${job.image_url || ''}">
                    </div>
                `;
            }

            // Update job
            async function updateJob() {
                const jobId = document.getElementById('edit-job-id').value;

                const formData = {
                    title: document.getElementById('edit-job-title').value.trim(),
                    department: document.getElementById('edit-job-department').value,
                    location: document.getElementById('edit-job-location').value.trim(),
                    type: document.getElementById('edit-job-type').value,
                    description: document.getElementById('edit-job-description').value.trim(),
                    benefits: document.getElementById('edit-job-benefits').value.trim(),
                    what_youll_do: document.getElementById('edit-job-what-youll-do').value.trim(),
                    what_youll_need: document.getElementById('edit-job-what-youll-need').value.trim(),
                    requirements: document.getElementById('edit-job-requirements').value.trim(),
                    image_url: document.getElementById('edit-job-image').value.trim()
                };

                // Validation
                if (!formData.title || !formData.department || !formData.location ||
                    !formData.type || !formData.description || !formData.benefits ||
                    !formData.what_youll_do || !formData.what_youll_need || !formData.requirements) {
                    showMessage('Please fill in all required fields.', 'danger');
                    return;
                }

                try {
                    showUpdateLoading();

                    const response = await fetch(`${ADMIN_API_URL}/${jobId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        credentials: 'include',
                        body: JSON.stringify(formData)
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to update job');
                    }

                    showMessage('Job updated successfully!', 'success');
                    $('#editJobModal').modal('hide');
                    loadJobs(); // Reload the jobs list

                } catch (error) {
                    console.error('Error updating job:', error);
                    showMessage('Error updating job: ' + error.message, 'danger');
                } finally {
                    hideUpdateLoading();
                }
            }

            // Delete job
            async function deleteJob(jobId, jobTitle) {
                if (!confirm(`Are you sure you want to delete the job "${jobTitle}"? This action cannot be undone.`)) {
                    return;
                }

                try {
                    const response = await fetch(`${ADMIN_API_URL}/${jobId}`, {
                        method: 'DELETE',
                        credentials: 'include'
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to delete job');
                    }

                    showMessage('Job deleted successfully!', 'success');
                    loadJobs(); // Reload the jobs list

                } catch (error) {
                    console.error('Error deleting job:', error);
                    showMessage('Error deleting job: ' + error.message, 'danger');
                }
            }

            // Update statistics
            function updateStats(jobs) {
                const totalJobs = jobs.length;
                const activeJobs = jobs.length; // All jobs are considered active
                const departments = [...new Set(jobs.map(job => job.department))].length;

                document.getElementById('total-jobs').textContent = totalJobs;
                document.getElementById('active-jobs').textContent = activeJobs;
                document.getElementById('departments').textContent = departments;
            }

            // ===== USER MANAGEMENT FUNCTIONS =====

            // Load and display users
            async function loadUsers() {
                try {
                    showUsersLoading();
                    hideUsersError();
                    hideUsersEmpty();

                    const response = await fetch(USERS_API_URL, {
                        method: 'GET',
                        credentials: 'include'
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const users = await response.json();

                    hideUsersLoading();

                    if (users.length === 0) {
                        showUsersEmpty();
                    } else {
                        displayUsers(users);
                    }

                } catch (error) {
                    console.error('Error loading users:', error);
                    hideUsersLoading();
                    showUsersError();
                }
            }

            // Display users in the list
            function displayUsers(users) {
                const container = document.getElementById('users-container');
                container.innerHTML = '';

                users.forEach(user => {
                    const userElement = createUserElement(user);
                    container.appendChild(userElement);
                });
            }

            // Create user element
            function createUserElement(user) {
                const userDiv = document.createElement('div');
                userDiv.className = 'job-item'; // Reuse job-item styling

                const isCurrentUser = user.username === document.getElementById('admin-username').textContent;
                const isDefaultUser = user.is_default_user;

                userDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="job-meta">
                                <span class="job-badge">${user.username}</span>
                                ${isCurrentUser ? '<span class="job-badge" style="background: #28a745; color: white;">Current User</span>' : ''}
                                ${isDefaultUser ? '<span class="job-badge" style="background: #FF9300; color: white;">Default User</span>' : ''}
                            </div>
                            <h5 style="color: #080a3c; margin-bottom: 8px;">
                                <i class='bx bx-user'></i> ${user.username}
                            </h5>
                            ${user.email ? `<p style="color: #4a6f8a; margin-bottom: 10px;">
                                <i class='bx bx-envelope'></i> ${user.email}
                            </p>` : ''}
                            <small style="color: #4a6f8a;">
                                <i class='bx bx-time'></i> Created: ${new Date(user.created_at).toLocaleDateString()}
                                ${user.last_login ? ` | Last login: ${new Date(user.last_login).toLocaleDateString()}` : ' | Never logged in'}
                            </small>
                        </div>
                        <div class="ml-3">
                            ${isCurrentUser ? `
                                <button class="btn btn-warning btn-sm mb-2" onclick="showChangePasswordModal()" title="Change Password">
                                    <i class='bx bx-key'></i> Change Password
                                </button>
                                <br>
                                <span class="text-muted" style="font-size: 12px;">Cannot delete<br>your own account</span>
                            ` : !isDefaultUser ? `
                                <button class="btn btn-danger btn-sm" onclick="deleteUser(${user.id}, '${user.username.replace(/'/g, "\\'")}')">
                                    <i class='bx bx-trash'></i> Delete
                                </button>
                            ` : `
                                <span class="text-muted" style="font-size: 12px;">Default user<br>cannot be deleted</span>
                            `}
                        </div>
                    </div>
                `;

                return userDiv;
            }

            // Show add user modal
            function showAddUserModal() {
                // Reset form
                document.getElementById('add-user-form').reset();
                $('#addUserModal').modal('show');
            }

            // Create new user
            async function createUser() {
                const username = document.getElementById('new-username').value.trim();
                const password = document.getElementById('new-password').value;
                const email = document.getElementById('new-email').value.trim();

                if (!username || !password) {
                    showMessage('Please enter both username and password.', 'danger');
                    return;
                }

                if (password.length < 6) {
                    showMessage('Password should be at least 6 characters long.', 'warning');
                    return;
                }

                try {
                    showCreateUserLoading();

                    const response = await fetch(USERS_API_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        credentials: 'include',
                        body: JSON.stringify({ username, password, email: email || null })
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to create user');
                    }

                    showMessage('User created successfully!', 'success');
                    $('#addUserModal').modal('hide');
                    loadUsers(); // Reload the users list

                } catch (error) {
                    console.error('Error creating user:', error);
                    showMessage('Error creating user: ' + error.message, 'danger');
                } finally {
                    hideCreateUserLoading();
                }
            }

            // Delete user
            async function deleteUser(userId, username) {
                if (!confirm(`Are you sure you want to delete the user "${username}"? This action cannot be undone.`)) {
                    return;
                }

                try {
                    const response = await fetch(`${USERS_API_URL}/${userId}`, {
                        method: 'DELETE',
                        credentials: 'include'
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to delete user');
                    }

                    showMessage('User deleted successfully!', 'success');
                    loadUsers(); // Reload the users list

                } catch (error) {
                    console.error('Error deleting user:', error);
                    showMessage('Error deleting user: ' + error.message, 'danger');
                }
            }

            // Show change password modal
            function showChangePasswordModal() {
                // Reset form
                document.getElementById('change-password-form').reset();
                $('#changePasswordModal').modal('show');
            }

            // Change password
            async function changePassword() {
                const currentPassword = document.getElementById('current-password').value;
                const newPassword = document.getElementById('new-password').value;
                const confirmPassword = document.getElementById('confirm-password').value;

                if (!currentPassword || !newPassword || !confirmPassword) {
                    showMessage('Please fill in all password fields.', 'danger');
                    return;
                }

                if (newPassword.length < 6) {
                    showMessage('New password should be at least 6 characters long.', 'warning');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    showMessage('New password and confirmation do not match.', 'danger');
                    return;
                }

                try {
                    showChangePasswordLoading();

                    const response = await fetch(`${API_BASE_URL}/admin/change-password`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        credentials: 'include',
                        body: JSON.stringify({ currentPassword, newPassword })
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to change password');
                    }

                    showMessage('Password changed successfully!', 'success');
                    $('#changePasswordModal').modal('hide');

                } catch (error) {
                    console.error('Error changing password:', error);
                    showMessage('Error changing password: ' + error.message, 'danger');
                } finally {
                    hideChangePasswordLoading();
                }
            }

            // Reset form
            function resetForm() {
                document.getElementById('job-form').reset();
            }

            // Show/hide loading states
            function showJobsLoading() {
                document.getElementById('jobs-loading').style.display = 'block';
                document.getElementById('jobs-container').innerHTML = '';
            }

            function hideJobsLoading() {
                document.getElementById('jobs-loading').style.display = 'none';
            }

            function showJobsError() {
                document.getElementById('jobs-error').style.display = 'block';
            }

            function hideJobsError() {
                document.getElementById('jobs-error').style.display = 'none';
            }

            function showJobsEmpty() {
                document.getElementById('jobs-empty').style.display = 'block';
            }

            function hideJobsEmpty() {
                document.getElementById('jobs-empty').style.display = 'none';
            }

            function showSubmitLoading() {
                document.getElementById('submit-text').style.display = 'none';
                document.getElementById('submit-loading').style.display = 'inline';
                document.querySelector('#job-form button[type="submit"]').disabled = true;
            }

            function hideSubmitLoading() {
                document.getElementById('submit-text').style.display = 'inline';
                document.getElementById('submit-loading').style.display = 'none';
                document.querySelector('#job-form button[type="submit"]').disabled = false;
            }

            function showUpdateLoading() {
                document.getElementById('update-text').style.display = 'none';
                document.getElementById('update-loading').style.display = 'inline';
                document.querySelector('#editJobModal .btn-primary').disabled = true;
            }

            function hideUpdateLoading() {
                document.getElementById('update-text').style.display = 'inline';
                document.getElementById('update-loading').style.display = 'none';
                document.querySelector('#editJobModal .btn-primary').disabled = false;
            }

            // User management UI helper functions
            function showUsersLoading() {
                document.getElementById('users-loading').style.display = 'block';
                document.getElementById('users-container').innerHTML = '';
            }

            function hideUsersLoading() {
                document.getElementById('users-loading').style.display = 'none';
            }

            function showUsersError() {
                document.getElementById('users-error').style.display = 'block';
            }

            function hideUsersError() {
                document.getElementById('users-error').style.display = 'none';
            }

            function showUsersEmpty() {
                document.getElementById('users-empty').style.display = 'block';
            }

            function hideUsersEmpty() {
                document.getElementById('users-empty').style.display = 'none';
            }

            function showCreateUserLoading() {
                document.getElementById('create-user-text').style.display = 'none';
                document.getElementById('create-user-loading').style.display = 'inline';
                document.querySelector('#addUserModal .btn-primary').disabled = true;
            }

            function hideCreateUserLoading() {
                document.getElementById('create-user-text').style.display = 'inline';
                document.getElementById('create-user-loading').style.display = 'none';
                document.querySelector('#addUserModal .btn-primary').disabled = false;
            }

            function showChangePasswordLoading() {
                document.getElementById('change-password-text').style.display = 'none';
                document.getElementById('change-password-loading').style.display = 'inline';
                document.querySelector('#changePasswordModal .btn-primary').disabled = true;
            }

            function hideChangePasswordLoading() {
                document.getElementById('change-password-text').style.display = 'inline';
                document.getElementById('change-password-loading').style.display = 'none';
                document.querySelector('#changePasswordModal .btn-primary').disabled = false;
            }

            // Section switching function
            function showSection(section) {
                // Hide all sections
                document.getElementById('jobs-section').style.display = 'none';
                document.getElementById('users-section').style.display = 'none';

                // Remove active class from all tabs
                document.getElementById('jobs-tab').classList.remove('active');
                document.getElementById('users-tab').classList.remove('active');

                // Show selected section and activate tab
                if (section === 'jobs') {
                    document.getElementById('jobs-section').style.display = 'block';
                    document.getElementById('jobs-tab').classList.add('active');
                    document.getElementById('jobs-tab').style.color = '#006EB3';
                    document.getElementById('users-tab').style.color = '#4a6f8a';
                } else if (section === 'users') {
                    document.getElementById('users-section').style.display = 'block';
                    document.getElementById('users-tab').classList.add('active');
                    document.getElementById('users-tab').style.color = '#006EB3';
                    document.getElementById('jobs-tab').style.color = '#4a6f8a';
                }
            }

            // Show message
            function showMessage(message, type) {
                const messageContainer = document.getElementById('message-container');
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                `;

                messageContainer.appendChild(alertDiv);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 5000);
            }
        </script>
    </body>
</html>
