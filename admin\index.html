<!doctype html>
<html lang="en">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Ed-admin Admin Login">
        <meta name="robots" content="noindex, nofollow">

        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="../assets/css/boxicons.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="../assets/css/style.css">

        <title>Ed-admin: Admin <PERSON>gin</title>
        <link rel="icon" type="image/png" href="../assets/img/favicon-Edadmin.ico">

        <!-- Custom Login CSS -->
        <style>
            body {
                background: linear-gradient(135deg, #006EB3 0%, #080a3c 100%);
                font-family: 'Poppins', sans-serif;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                padding: 20px;
            }

            .login-container {
                background: white;
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                width: 100%;
                max-width: 450px;
                position: relative;
                overflow: hidden;
            }

            .login-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 5px;
                background: linear-gradient(90deg, #006EB3 0%, #FF9300 100%);
            }

            .login-header {
                text-align: center;
                margin-bottom: 40px;
            }

            .login-header h1 {
                color: #080a3c;
                font-weight: 700;
                font-size: 2rem;
                margin-bottom: 10px;
            }

            .login-header p {
                color: #4a6f8a;
                font-size: 1rem;
                margin: 0;
            }

            .form-group {
                margin-bottom: 25px;
            }

            .form-group label {
                color: #080a3c;
                font-weight: 600;
                margin-bottom: 8px;
                display: block;
            }

            .form-control {
                border-radius: 10px;
                border: 2px solid #e9ecef;
                padding: 15px 20px;
                font-size: 16px;
                transition: all 0.3s ease;
                background: #f8f9fa;
            }

            .form-control:focus {
                border-color: #006EB3;
                box-shadow: 0 0 0 0.2rem rgba(0, 107, 179, 0.25);
                background: white;
            }

            .input-group {
                position: relative;
            }

            .input-group .form-control {
                padding-left: 50px;
            }

            .input-group-prepend {
                position: absolute;
                left: 15px;
                top: 50%;
                transform: translateY(-50%);
                z-index: 10;
                color: #4a6f8a;
                font-size: 18px;
            }

            .btn-login {
                background: linear-gradient(135deg, #006EB3 0%, #0056a3 100%);
                border: none;
                border-radius: 10px;
                padding: 15px 30px;
                font-weight: 600;
                font-size: 16px;
                color: white;
                width: 100%;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .btn-login::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #FF9300 0%, #e6840a 100%);
                transition: left 0.3s ease;
                z-index: 1;
            }

            .btn-login span {
                position: relative;
                z-index: 2;
            }

            .btn-login:hover::before {
                left: 0;
            }

            .btn-login:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(0, 107, 179, 0.3);
            }

            .btn-login:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }

            .alert {
                border-radius: 10px;
                border: none;
                padding: 15px 20px;
                margin-bottom: 25px;
            }

            .alert-danger {
                background-color: #f8d7da;
                color: #721c24;
                border-left: 4px solid #dc3545;
            }

            .loading-spinner {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid #ffffff;
                border-top: 3px solid transparent;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-right: 10px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .demo-credentials {
                background: #e8f4fd;
                border-radius: 10px;
                padding: 20px;
                margin-top: 30px;
                border-left: 4px solid #006EB3;
            }

            .demo-credentials h6 {
                color: #006EB3;
                font-weight: 600;
                margin-bottom: 10px;
            }

            .demo-credentials p {
                color: #4a6f8a;
                margin: 5px 0;
                font-size: 14px;
            }

            .demo-credentials code {
                background: #006EB3;
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 13px;
            }
        </style>
    </head>

    <body>
        <div class="login-container">
            <div class="login-header">
                <h1><i class='bx bx-shield-check'></i> Admin Login</h1>
                <p>Access the Ed-admin job management system</p>
            </div>

            <!-- Error Message -->
            <div id="error-message" style="display: none;"></div>

            <!-- Login Form -->
            <form id="login-form">
                <div class="form-group">
                    <label for="username">Username</label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <i class='bx bx-user'></i>
                        </div>
                        <input type="text" class="form-control" id="username" name="username" required autocomplete="username">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <i class='bx bx-lock-alt'></i>
                        </div>
                        <input type="password" class="form-control" id="password" name="password" required autocomplete="current-password">
                    </div>
                </div>

                <button type="submit" class="btn btn-login" id="login-btn">
                    <span id="login-text">
                        <i class='bx bx-log-in'></i> Login
                    </span>
                    <span id="login-loading" style="display: none;">
                        <span class="loading-spinner"></span> Logging in...
                    </span>
                </button>
            </form>


        </div>

        <!-- Scripts -->
        <script src="../assets/js/jquery.min.js"></script>
        <script src="../assets/js/bootstrap.min.js"></script>

        <!-- Login JavaScript -->
        <script>
            // API Configuration
            const API_BASE_URL = 'https://job-post-backend-zoku.onrender.com/api';
            const LOGIN_API_URL = `${API_BASE_URL}/admin/login`;
            const TOKEN_KEY = 'edAdminToken';
            const USER_KEY = 'edAdminUser';

            // Token Management Class
            class AuthManager {
                static setToken(token) {
                    localStorage.setItem(TOKEN_KEY, token);
                    console.log('Token stored in localStorage');
                }

                static getToken() {
                    return localStorage.getItem(TOKEN_KEY);
                }

                static setUser(user) {
                    localStorage.setItem(USER_KEY, JSON.stringify(user));
                }

                static getUser() {
                    const user = localStorage.getItem(USER_KEY);
                    return user ? JSON.parse(user) : null;
                }

                static clearAuth() {
                    localStorage.removeItem(TOKEN_KEY);
                    localStorage.removeItem(USER_KEY);
                    console.log('Auth data cleared from localStorage');
                }

                static isAuthenticated() {
                    return !!this.getToken();
                }

                static getAuthHeaders() {
                    const token = this.getToken();
                    return token ? { 'Authorization': `Bearer ${token}` } : {};
                }
            }

            // Initialize page
            document.addEventListener('DOMContentLoaded', function() {
                // Check for login message from redirect
                const loginMessage = sessionStorage.getItem('loginMessage');
                if (loginMessage) {
                    showError(loginMessage);
                    sessionStorage.removeItem('loginMessage');
                }

                // Check if already logged in
                checkExistingSession();

                // Setup form submission
                document.getElementById('login-form').addEventListener('submit', handleLogin);

                // Focus on username field
                document.getElementById('username').focus();
            });

            // Check if user is already logged in
            async function checkExistingSession() {
                try {
                    console.log('Checking for existing authentication...');

                    if (!AuthManager.isAuthenticated()) {
                        console.log('No auth token found, showing login form');
                        return;
                    }

                    console.log('Auth token found, verifying with server...');
                    const response = await fetch(`${API_BASE_URL}/admin/verify-token`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...AuthManager.getAuthHeaders()
                        }
                    });

                    console.log('Token verification response status:', response.status);

                    if (response.ok) {
                        const result = await response.json();
                        console.log('Token verification result:', result);

                        if (result.valid) {
                            console.log('User already authenticated, redirecting to jobs page...');
                            // Update user info if provided
                            if (result.user) {
                                AuthManager.setUser(result.user);
                            }
                            window.location.href = 'jobs.html';
                            return;
                        }
                    }

                    console.log('Token invalid or expired, clearing auth and showing login form');
                    AuthManager.clearAuth();
                } catch (error) {
                    console.log('Session check failed:', error);
                    AuthManager.clearAuth();
                }
            }

            // Handle login form submission
            async function handleLogin(event) {
                event.preventDefault();

                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;

                if (!username || !password) {
                    showError('Please enter both username and password.');
                    return;
                }

                try {
                    showLoading();
                    hideError();

                    console.log('Attempting login for user:', username);

                    const response = await fetch(LOGIN_API_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ username, password })
                    });

                    console.log('Login response status:', response.status);
                    const result = await response.json();
                    console.log('Login response:', result);

                    if (response.ok && result.success) {
                        // Login successful
                        console.log('Login successful, storing auth data...');
                        showSuccess('Login successful! Redirecting...');

                        // Store authentication data
                        if (result.token) {
                            AuthManager.setToken(result.token);
                        }
                        if (result.admin) {
                            AuthManager.setUser(result.admin);
                        }

                        // Immediate redirect - no need to wait for cookies
                        console.log('Auth data stored, redirecting to jobs page...');
                        setTimeout(() => {
                            window.location.href = 'jobs.html';
                        }, 500); // Short delay just for user feedback
                    } else {
                        // Login failed
                        console.log('Login failed:', result.error);
                        showError(result.error || 'Login failed. Please check your credentials.');
                    }

                } catch (error) {
                    console.error('Login error:', error);
                    showError('Unable to connect to the server. Please try again later.');
                } finally {
                    hideLoading();
                }
            }

            // Show loading state
            function showLoading() {
                document.getElementById('login-text').style.display = 'none';
                document.getElementById('login-loading').style.display = 'inline';
                document.getElementById('login-btn').disabled = true;
            }

            // Hide loading state
            function hideLoading() {
                document.getElementById('login-text').style.display = 'inline';
                document.getElementById('login-loading').style.display = 'none';
                document.getElementById('login-btn').disabled = false;
            }

            // Show error message
            function showError(message) {
                const errorDiv = document.getElementById('error-message');
                errorDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class='bx bx-error'></i> ${message}
                    </div>
                `;
                errorDiv.style.display = 'block';
            }

            // Hide error message
            function hideError() {
                document.getElementById('error-message').style.display = 'none';
            }

            // Show success message
            function showSuccess(message) {
                const errorDiv = document.getElementById('error-message');
                errorDiv.innerHTML = `
                    <div class="alert alert-success" style="background-color: #d4edda; color: #155724; border-left: 4px solid #28a745;">
                        <i class='bx bx-check'></i> ${message}
                    </div>
                `;
                errorDiv.style.display = 'block';
            }

            // Handle Enter key press
            document.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    document.getElementById('login-form').dispatchEvent(new Event('submit'));
                }
            });
        </script>
    </body>
</html>