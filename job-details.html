<!doctype html>
<html lang="en">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Job Details - Ed-admin Career Opportunities">
        <meta name="keywords" content="Ed-admin careers, education software jobs, edtech careers, job details">
        <meta name="robots" content="index, follow">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
        
        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="assets/css/responsive.css">

        <title>Job Details - Ed-admin Careers</title>
        <link rel="icon" type="image/png" href="assets/img/favicon-Edadmin.ico">

        <!-- Mobile Responsive CSS -->
        <style>
            /* Mobile Responsive Styles */
            @media only screen and (max-width: 767px) {
                /* Mobile Navigation */
                .navbar-area {
                    padding: 10px 0;
                    background-color: rgba(255, 255, 255, 0.95);
                }

                .spacle-responsive-nav {
                    display: block !important;
                }

                .spacle-nav {
                    display: none !important;
                }

                /* Main section mobile styles */
                section[style*="padding: 80px 0"] {
                    padding: 40px 0 !important;
                }

                /* Container mobile styles */
                div[style*="max-width: 1200px"] {
                    max-width: 100% !important;
                    padding: 0 15px !important;
                }

                /* Header section mobile styles */
                div[style*="position: relative; right: 100px"] {
                    position: static !important;
                    right: auto !important;
                }

                /* Job title mobile styles */
                h1[id="job-title"] {
                    font-size: 1.25rem !important;
                    text-align: center !important;
                    width: 100% !important;
                    margin-bottom: 20px !important;
                }

                /* Overlaying blocks mobile styles */
                div[style*="width: 80%; height: 100px; position: relative"] {
                    width: 100% !important;
                    height: 80px !important;
                    margin-bottom: 20px;
                }

                /* Individual overlay blocks mobile styles */
                div[style*="width: 100%; height: 100px; background: rgba(63, 81, 181"] {
                    height: 80px !important;
                    display: none !important;
                }

                div[style*="width: 100%; height: 100px; background-color: rgb(63,81,181)"] {
                    height: 80px !important;
                    left: 0 !important;
                    width: 30% !important;
                }

                div[id="job-header-image"] {
                    height: 80px !important;
                    width: 70% !important;
                    left: 30% !important;
                }

                /* Grid layout mobile styles - stack vertically */
                div[style*="display: grid; grid-template-columns: 1fr 1fr"] {
                    display: block !important;
                    gap: 20px !important;
                }

                /* Content sections mobile styles */
                div[style*="padding: 30px"] {
                    padding: 20px 15px !important;
                    margin-bottom: 20px;
                    border-bottom: 1px solid #eee;
                }

                /* Section headings mobile styles */
                h3[style*="font-size: 1.5rem"] {
                    font-size: 1.25rem !important;
                    margin-bottom: 15px !important;
                }

                /* Text content mobile styles */
                p[style*="color: #4a6f8a"] {
                    font-size: 14px !important;
                    line-height: 1.6 !important;
                }

                li[style*="color: #4a6f8a"] {
                    font-size: 14px !important;
                    line-height: 1.6 !important;
                    padding: 6px 0 !important;
                }

                /* Apply section mobile styles */
                div[style*="width: 100%; display: flex; justify-content: flex-end"] {
                    width: 100% !important;
                    justify-content: center !important;
                    margin-top: 40px !important;
                }

                /* Apply button mobile styles */
                div[onclick="applyForJob()"] {
                    width: 200px !important;
                    height: 45px !important;
                    font-size: 16px !important;
                    font-weight: 600;
                }
            }

            /* Apply button hover effect */
            .apply-button-details:hover {
                background-color: rgb(0, 110, 179) !important;
                border-color: rgb(0, 110, 179) !important;
                color: white !important;
                transition: all 0.3s ease;
            }

            /* Tablet Responsive Styles */
            @media only screen and (min-width: 768px) and (max-width: 991px) {
                /* Main section tablet styles */
                section[style*="padding: 80px 0"] {
                    padding: 60px 0 !important;
                }

                /* Container tablet styles */
                div[style*="max-width: 1200px"] {
                    max-width: 95% !important;
                    padding: 0 20px !important;
                }

                /* Header section tablet styles */
                div[style*="position: relative; right: 100px"] {
                    right: 50px !important;
                }

                /* Job title tablet styles */
                h1[id="job-title"] {
                    font-size: 1.4rem !important;
                    width: 90% !important;
                }

                /* Overlaying blocks tablet styles */
                div[style*="width: 80%; height: 100px; position: relative"] {
                    width: 90% !important;
                }

                /* Grid layout tablet styles */
                div[style*="display: grid; grid-template-columns: 1fr 1fr"] {
                    gap: 30px !important;
                }

                /* Content sections tablet styles */
                div[style*="padding: 30px"] {
                    padding: 25px !important;
                }

                /* Apply section tablet styles */
                div[style*="width: 100%; display: flex; justify-content: flex-end"] {
                    width: 100% !important;
                }
            }
        </style>
        
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-B7QM9WG2P4');
        </script>
    </head>

    <body>
        <span data-menuid="about-Ed-admin" class="d-none"></span>
        <!-- Start PopUps Area -->
            <div data-include="popups/demonow"></div>
            <div data-include="popups/bookdemo"></div>
            <div data-include="popups/downloadnow"></div>
            <div data-include="popups/freedemo"></div>
        <!-- End PopUps Area -->

        <!-- Start Header Area -->
        <div data-include="common/header2"></div>
        <!-- End Header Area -->

        <!-- Original Template Structure - Exactly as designed -->
        <section style="padding: 80px 0; background-color: #ffffff;">
            <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">

                <!-- Job Title Section - Positioned to the left above overlaying blocks -->
                

                <!-- Overlaying Blocks Section - Centered to match website alignment -->
                <div style=" position: relative; right: 100px;">
                    <div style="height: auto; width: 100%; position: relative; display: flex; justify-content: center; align-items: center; margin-bottom: 40px; flex-direction: column;">
                        <h1 id="job-title" style="font-size: 1.5rem; font-weight: 600; color: #333; margin-bottom: 10px; text-align: left; width: 80%; text-align: left;">QA Automation Engineer</h1>
                        <div style="width: 80%; height: 100px; position: relative;">
                            <div style="width: 100%; height: 100px; background: rgba(63, 81, 181, 0.76); border-radius: 35px; overflow: hidden; position: absolute; top: 0; left: 0;"></div>
                            <div style="width: 100%; height: 100px; background: rgba(63, 81, 181, 0.65); border-radius: 35px; overflow: hidden; position: absolute; top: 0; left: 70px;"></div>
                            <div style="width: 100%; height: 100px; background-color: rgb(63,81,181); border-radius: 35px; overflow: hidden; position: absolute; top: 0; left: 150px;"></div>
                            <div id="job-header-image" style="width: 72%; height: 100px; background-image: url('./assets/img/qa-engineer.png'); background-position: center; background-size: cover; background-repeat: no-repeat; border-radius: 35px; overflow: hidden; position: absolute; top: 0; left: 420px;"></div>
                     </div>
                     </div>
                </div>

                <!-- 4 Content Sections in 2x2 Grid -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px;">

                    <!-- Description Section -->
                    <div style="padding: 30px;">
                        <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 20px; color: #080a3c;">Description</h3>
                        <div id="job-description">
                            <p style="color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English.</p>
                            <p style="color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here'.</p>
                        </div>
                    </div>

                    <!-- Benefits Section -->
                    <div style="padding: 30px;">
                        <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 20px; color: #080a3c;">Benefits</h3>
                        <ul id="job-benefits" style="list-style: disc; padding-left: 20px;">
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">It is a long established fact that</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">reader will be distracted by the</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">of a page when looking at its</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">of a page when looking at its</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">readable content of a page</li>
                        </ul>
                    </div>

                    <!-- What You'll Do Section -->
                    <div style="padding: 30px;">
                        <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 20px; color: #080a3c;">What You'll Do</h3>
                        <ul id="job-what-youll-do" style="list-style: disc; padding-left: 20px;">
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">it is a long established fact that</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">reader will be distracted by the</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">of a page when looking at its</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6; font-family: 'Poppins', sans-serif;">readable content of a page</li>
                        </ul>
                    </div>

                    <!-- What You'll Need Section -->
                    <div style="padding: 30px;">
                        <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 20px; color: #080a3c;">What You'll Need</h3>
                        <ul id="job-what-youll-need" style="list-style: disc; padding-left: 20px;">
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">it is a long established fact that</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">reader will be distracted by the</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of a page when looking at its</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">readable content of a page</li>
                        </ul>
                    </div>
                </div>

                <!-- Apply Section - Aligned with content sections above -->
                <div style="width: 100%; display: flex; justify-content: flex-end; margin-top: 30px;">
                    <div onclick="applyForJob()" class="apply-button-details" style="width: 250px; height: 50px; border-radius: 10px; border: 1.5px; border-color: black; border-style: solid; display: flex; justify-content: center; align-items: center; cursor: pointer; transition: all 0.3s ease; background-color: transparent;">Apply Now</div>
                </div>
            </div>
        </section>

        <!-- Start Footer Area -->
        <div data-include="common/footer"></div>
        <!-- End Footer Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>

        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="assets/js/particles.min.js"></script>
        <script src="assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="assets/js/main.js"></script>

        <!-- Job Details JavaScript -->
        <script>
            // API Configuration
            const JOBS_API_URL = 'https://job-post-backend-zoku.onrender.com/api/jobs';

            // Get job ID from URL parameters
            function getJobIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('id');
            }

            // Load job details
            async function loadJobDetails() {
                const jobId = getJobIdFromUrl();

                if (!jobId) {
                    showError('No job ID specified');
                    return;
                }

                try {
                    const response = await fetch(`${JOBS_API_URL}/${jobId}`);

                    if (!response.ok) {
                        throw new Error('Job not found');
                    }

                    const job = await response.json();
                    displayJobDetails(job);

                } catch (error) {
                    console.error('Error loading job details:', error);
                    showError('Unable to load job details. Please try again later.');
                }
            }

            // Display job details - preserve original template styling
            function displayJobDetails(job) {
                // Update job title
                document.getElementById('job-title').textContent = job.title;

                // Update header image
                if (job.image_url) {
                    document.getElementById('job-header-image').style.backgroundImage = `url('${job.image_url}')`;
                }

                // Update description - preserve paragraph styling
                document.getElementById('job-description').innerHTML = formatDescriptionContent(job.description);

                // Update benefits - preserve list styling
                document.getElementById('job-benefits').innerHTML = formatListContent(job.benefits);

                // Update what you'll do - preserve list styling
                document.getElementById('job-what-youll-do').innerHTML = formatListContent(job.what_youll_do);

                // Update what you'll need - preserve list styling
                document.getElementById('job-what-youll-need').innerHTML = formatListContent(job.what_youll_need);

                // Update page title
                document.title = `${job.title} - Ed-admin Careers`;
            }

            // Format description content - preserve original paragraph styling with mobile responsiveness
            function formatDescriptionContent(text) {
                const isMobile = window.innerWidth <= 767;
                const fontSize = isMobile ? '14px' : '16px';

                if (!text) return `<p style="color: #4a6f8a; line-height: 1.6; font-size: ${fontSize};">No description available.</p>`;

                // Split by periods and create paragraphs with responsive styling
                const sentences = text.split('.').filter(s => s.trim().length > 0);
                const paragraphs = [];

                for (let i = 0; i < sentences.length; i += 2) {
                    const paragraph = sentences.slice(i, i + 2).join('.') + '.';
                    paragraphs.push(`<p style="color: #4a6f8a; line-height: 1.6; font-size: ${fontSize}; font-family: 'Poppins', sans-serif;">${paragraph}</p>`);
                }

                return paragraphs.join('');
            }

            // Format list content - split by new lines (or bullets for backward compatibility) and create proper bullet points with mobile responsiveness
            function formatListContent(text) {
                const isMobile = window.innerWidth <= 767;
                const fontSize = isMobile ? '14px' : '16px';
                const padding = isMobile ? '6px 0' : '8px 0';

                if (!text) return `<li style="padding: ${padding}; color: #4a6f8a; line-height: 1.6; font-size: ${fontSize};">No information available.</li>`;

                // Split by new lines first, then by bullet symbols for backward compatibility
                let items;
                if (text.includes('\n')) {
                    // New format: split by new lines
                    items = text.split('\n').filter(item => item.trim().length > 0);
                } else {
                    // Old format: split by bullet symbols for backward compatibility
                    items = text.split('•').filter(item => item.trim().length > 0);
                }

                return items.map(item =>
                    `<li style="padding: ${padding}; color: #4a6f8a; line-height: 1.6; font-size: ${fontSize}; font-family: 'Poppins', sans-serif;">${item.trim()}</li>`
                ).join('');
            }

            // Show error message with mobile responsiveness
            function showError(message) {
                const isMobile = window.innerWidth <= 767;
                const padding = isMobile ? '40px 15px' : '60px 20px';
                const iconSize = isMobile ? '3rem' : '4rem';
                const headingSize = isMobile ? '1.5rem' : '2rem';
                const textSize = isMobile ? '14px' : '16px';

                const container = document.querySelector('section');
                container.innerHTML = `
                    <div style="text-align: center; padding: ${padding};">
                        <i class='bx bx-error' style="font-size: ${iconSize}; color: #dc3545; margin-bottom: 20px;"></i>
                        <h2 style="color: #dc3545; margin-bottom: 15px; font-size: ${headingSize};">Error</h2>
                        <p style="color: #666; margin-bottom: 30px; font-size: ${textSize}; line-height: 1.6;">${message}</p>
                        <a href="join-our-team.html" style="display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-size: ${textSize};">
                            <i class='bx bx-arrow-back'></i>
                            Back to Jobs
                        </a>
                    </div>
                `;
            }

            // Apply for job function
            function applyForJob() {
                const jobId = getJobIdFromUrl();
                if (jobId) {
                    // Redirect to job application page with job ID
                    window.location.href = `Job-apply.html?jobId=${jobId}`;
                } else {
                    alert('Unable to apply for this job. Please try again.');
                }
            }

            // Load job details when page loads
            document.addEventListener('DOMContentLoaded', function() {
                loadJobDetails();
            });

            // Handle window resize for responsive adjustments
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    // Reload job details to adjust responsive styling
                    const jobId = new URLSearchParams(window.location.search).get('id');
                    if (jobId) {
                        loadJobDetails();
                    }
                }, 250);
            });
        </script>
    </body>
</html>
