<!doctype html>
<html lang="en">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Job Details - Ed-admin Career Opportunities">
        <meta name="keywords" content="Ed-admin careers, education software jobs, edtech careers, job details">
        <meta name="robots" content="index, follow">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
        
        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="assets/css/responsive.css">

        <title>Apply for Job - Ed-admin Careers</title>
        <link rel="icon" type="image/png" href="assets/img/favicon-Edadmin.ico">

        <!-- Custom CSS for spinner animation and mobile responsiveness -->
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* Desktop form styling to match contact.html */
            .contact-form .form-control {
                width: 400px;
                margin-bottom: 15px;
            }

            /* File upload styling */
            .file-upload-container {
                width: 324px;
            }

            .file-upload-display {
                width: 324px;
                height: 138px;
            }

            /* Mobile Responsive Styles */
            @media only screen and (max-width: 767px) {
                /* Prevent horizontal overflow */
                body {
                    overflow-x: hidden !important;
                    width: 100% !important;
                    max-width: 100vw !important;
                }

                * {
                    box-sizing: border-box !important;
                }

                /* Mobile Navigation */
                .navbar-area {
                    padding: 10px 0;
                    background-color: rgba(255, 255, 255, 0.95);
                }

                .spacle-responsive-nav {
                    display: block !important;
                }

                .spacle-nav {
                    display: none !important;
                }

                /* Main section mobile styles */
                section[style*="height: 700px"] {
                    height: auto !important;
                    padding: 40px 15px !important;
                    flex-direction: column !important;
                    gap: 30px !important;
                }

                /* Form container mobile styles */
                div[style*="height: 500px; width: 500px; background-color: #ffffff"] {
                    height: auto !important;
                    width: 100% !important;
                    max-width: 350px !important;
                    padding: 25px 15px !important;
                    border-radius: 15px !important;
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
                    margin-bottom: 20px;
                    box-sizing: border-box !important;
                    overflow: hidden !important;
                }

                /* Form heading mobile styles */
                div[style*="height: 500px; width: 500px; background-color: #ffffff"] h2 {
                    font-size: 1.5rem !important;
                    text-align: center !important;
                    margin-bottom: 10px !important;
                }

                /* Job title display mobile styles */
                #job-title-display {
                    font-size: 14px !important;
                    text-align: center !important;
                    margin-bottom: 25px !important;
                    color: #666 !important;
                }

                /* Form inputs mobile styles */
                .contact-form .form-control {
                    width: 100% !important;
                    margin-bottom: 15px !important;
                }

                /* File upload container mobile styles */
                .file-upload-container {
                    width: 100% !important;
                    max-width: 100% !important;
                    box-sizing: border-box !important;
                }

                .file-upload-display {
                    width: 100% !important;
                    max-width: 100% !important;
                    height: 100px !important;
                    border-radius: 8px !important;
                    font-size: 14px !important;
                    box-sizing: border-box !important;
                }

                /* Ensure all form elements stay within container */
                #job-application-form {
                    width: 100% !important;
                    max-width: 100% !important;
                    box-sizing: border-box !important;
                }

                #job-application-form * {
                    box-sizing: border-box !important;
                }

                /* Image container mobile styles */
                div[style*="height: 500px; width: 500px; position: relative"] {
                    height: 250px !important;
                    width: 100% !important;
                    max-width: 300px !important;
                    margin: 0 auto !important;
                }

                /* Background shapes mobile styles */
                div[style*="width: 250px; height: 350px; background-color: #006EB3"] {
                    width: 150px !important;
                    height: 200px !important;
                    left: 30px !important;
                    bottom: 0 !important;
                    border-bottom-left-radius: 60px !important;
                }

                div[style*="width: 300px; height: 250px; background-image"] {
                    width: 200px !important;
                    height: 150px !important;
                    top: 20px !important;
                    left: 20px !important;
                    border-bottom-left-radius: 60px !important;
                }

                /* Submit button container mobile styles */
                div[style*="display: flex; justify-content: center; align-items: center; margin-bottom: 50px"] {
                    margin-bottom: 30px !important;
                    padding: 0 15px !important;
                }

                /* Submit button mobile styles */
                button[style*="width: 230px; height: 40px"] {
                    width: 100% !important;
                    max-width: 280px !important;
                    height: 45px !important;
                    font-size: 16px !important;
                    font-weight: 600 !important;
                    border: 2px solid #007bff !important;
                    color: #007bff !important;
                    background-color: transparent !important;
                    transition: all 0.3s ease !important;
                }

                button[style*="width: 230px; height: 40px"]:hover {
                    background-color: #007bff !important;
                    color: white !important;
                }

                /* Form labels mobile styles */
                label[style*="display: block"] {
                    font-size: 14px !important;
                    margin-bottom: 8px !important;
                }

                /* File format info mobile styles */
                div[style*="margin-top: 5px; font-size: 12px"] {
                    font-size: 11px !important;
                    text-align: center !important;
                    margin-top: 8px !important;
                }
            }

            /* Tablet Responsive Styles */
            @media only screen and (min-width: 768px) and (max-width: 991px) {
                /* Main section tablet styles */
                section[style*="height: 700px"] {
                    height: auto !important;
                    padding: 60px 30px !important;
                    gap: 40px !important;
                }

                /* Form container tablet styles */
                div[style*="height: 500px; width: 500px; background-color: #ffffff"] {
                    height: auto !important;
                    width: 450px !important;
                    padding: 40px 30px !important;
                    border-radius: 15px !important;
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
                }

                /* Form inputs tablet styles */
                .contact-form .form-control {
                    width: 100% !important;
                    max-width: 380px !important;
                    box-sizing: border-box !important;
                }

                /* File upload container tablet styles */
                .file-upload-container {
                    width: 100% !important;
                    max-width: 380px !important;
                    box-sizing: border-box !important;
                }

                .file-upload-display {
                    width: 100% !important;
                    max-width: 300px !important;
                    height: 120px !important;
                    box-sizing: border-box !important;
                }

                /* Image container tablet styles */
                div[style*="height: 500px; width: 500px; position: relative"] {
                    height: 400px !important;
                    width: 400px !important;
                }

                /* Background shapes tablet styles */
                div[style*="width: 250px; height: 350px; background-color: #006EB3"] {
                    width: 200px !important;
                    height: 280px !important;
                    left: 40px !important;
                    border-bottom-left-radius: 80px !important;
                }

                div[style*="width: 300px; height: 250px; background-image"] {
                    width: 250px !important;
                    height: 200px !important;
                    border-bottom-left-radius: 80px !important;
                }

                /* Submit button tablet styles */
                button[style*="width: 230px; height: 40px"] {
                    width: 250px !important;
                    height: 42px !important;
                }
            }
        </style>
        
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-B7QM9WG2P4');
        </script>
    </head>

    <body>
        <span data-menuid="about-Ed-admin" class="d-none"></span>
        <!-- Start PopUps Area -->
            <div data-include="popups/demonow"></div>
            <div data-include="popups/bookdemo"></div>
            <div data-include="popups/downloadnow"></div>
            <div data-include="popups/freedemo"></div>
        <!-- End PopUps Area -->

        <!-- Start Header Area -->
        <div data-include="common/header2"></div>
        <!-- End Header Area -->

        <section style="height: 700px; display: flex; justify-content: center; align-items: center; flex-wrap: wrap;">
            <div style="height: 500px; width: 500px; background-color: #ffffff; display: flex; flex-direction: column;  align-items: left; gap: 20px;">
                <div>
                    <h2>Apply for this role</h2>
                    <p id="job-title-display">Loading job information...</p>
                </div>
                <div class="contact-form">
                    <form id="job-application-form" enctype="multipart/form-data">
                    <div style="margin-bottom: 20px;">
                        <div class="form-group">
                            <input type="text" name="name" id="name" class="form-control" required data-error="Please enter your name" placeholder="Your Name">
                            <div class="help-block with-errors"></div>
                        </div>
                        <div class="form-group">
                            <input type="email" name="email" id="email" class="form-control" required data-error="Please enter your email" placeholder="Your Email">
                            <div class="help-block with-errors"></div>
                        </div>
                        <div class="form-group">
                            <input type="url" name="linkedin-link" id="linkedin-link" class="form-control" data-error="Please enter a valid LinkedIn URL" placeholder="LinkedIn link (Optional)">
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <div class="form-group">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">CV or resume</label>
                            <div class="file-upload-container" style="position: relative;">
                                <input type="file" id="cv-upload" name="cv-upload" accept=".pdf,.doc,.docx" style="position: absolute; opacity: 0; width: 100%; height: 100%; cursor: pointer;" onchange="handleFileUpload(this)" required data-error="Please upload your CV or resume">
                                <div id="file-upload-display" class="file-upload-display" style="border: 1px solid #ddd; border-radius: 4px; display: flex; flex-direction: column; justify-content: flex-start; align-items: flex-start; padding: 12px; background-color: #fff; cursor: pointer; transition: border-color 0.3s;" onclick="document.getElementById('cv-upload').click();">
                                    <span id="file-upload-text" style="color: #666; font-family: Poppins; font-weight: 300; font-size: 16px; line-height: 100%; letter-spacing: 0%; margin-bottom: 8px;">Click to upload or <strong style="font-family: Poppins; font-weight: 500; font-size: 16px; line-height: 100%; letter-spacing: 0%;">drag and drop</strong></span>
                                    <svg width="22" height="28" viewBox="0 0 22 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5.66659 22H16.3333V19.3333H5.66659V22ZM5.66659 16.6666H16.3333V14H5.66659V16.6666ZM2.99992 27.3333C2.26659 27.3333 1.63881 27.0722 1.11659 26.55C0.594363 26.0277 0.333252 25.4 0.333252 24.6666V3.33329C0.333252 2.59996 0.594363 1.97218 1.11659 1.44996C1.63881 0.927737 2.26659 0.666626 2.99992 0.666626H13.6666L21.6666 8.66663V24.6666C21.6666 25.4 21.4055 26.0277 20.8833 26.55C20.361 27.0722 19.7333 27.3333 18.9999 27.3333H2.99992ZM12.3333 9.99996V3.33329H2.99992V24.6666H18.9999V9.99996H12.3333Z" fill="#C2C2C2"/>
                                    </svg>
                                </div>
                            </div>
                            <div style="margin-top: 5px; font-size: 12px; color: #888;">Supported formats: PDF, DOC, DOCX (Max 5MB)</div>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>


                    </form>
                </div>
            </div>
            <div style="height: 500px; width: 500px; position: relative;">
                <div style="width: 250px; height: 350px; background-color: #006EB3; position: absolute; bottom: 10; left: 50px; border-bottom-left-radius: 30px;"></div>
                <div style="width: 300px; height: 250px; background-image: url('./assets/img/apply-image.jpg'); background-position: center; background-size: cover; background-repeat: no-repeat; position: absolute; top: 0; left: 0; border-bottom-left-radius: 100px;" ></div>

            </div>

        </section>
       <div style="display: flex; justify-content: center; align-items: center; margin-bottom: 50px;">
             <button id="submit-application" type="button" style="width: 230px; height: 40px; background-color: transparent; border-radius: 10px; cursor: pointer; transition: background-color 0.3s;" onclick="submitApplicationForm()">
                <span id="submit-text">Submit application</span>
                <i id="submit-spinner" class='bx bx-loader-alt' style="display: none; margin-left: 8px; animation: spin 1s linear infinite;"></i>
             </button>
       </div>

        <!-- Start Footer Area -->
        <div data-include="common/footer"></div>
        <!-- End Footer Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>

        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="assets/js/particles.min.js"></script>
        <script src="assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="assets/js/main.js"></script>

        <!-- Job Application JavaScript -->
        <script>
            // API Configuration
            const JOBS_API_URL = 'https://job-post-backend-zoku.onrender.com/api/jobs';
            const APPLICATION_API_URL = 'https://job-post-backend-zoku.onrender.com/api/applications';

            let currentJob = null;

            // Get job ID from URL parameters
            function getJobIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('jobId');
            }

            // Load job details
            async function loadJobDetails() {
                const jobId = getJobIdFromUrl();

                if (!jobId) {
                    showError('No job ID specified');
                    return;
                }

                try {
                    const response = await fetch(`${JOBS_API_URL}/${jobId}`);

                    if (!response.ok) {
                        throw new Error('Job not found');
                    }

                    currentJob = await response.json();
                    displayJobInfo(currentJob);

                } catch (error) {
                    console.error('Error loading job details:', error);
                    showError('Unable to load job details. Please try again later.');
                }
            }

            // Display job information with mobile responsiveness
            function displayJobInfo(job) {
                const isMobile = window.innerWidth <= 767;
                const jobTitleElement = document.getElementById('job-title-display');

                if (isMobile) {
                    jobTitleElement.textContent = `Position: ${job.title}`;
                } else {
                    jobTitleElement.textContent = job.title;
                }

                document.title = `Apply for ${job.title} - Ed-admin Careers`;
            }

            // Show error message with mobile responsiveness
            function showError(message) {
                const jobTitleElement = document.getElementById('job-title-display');
                const isMobile = window.innerWidth <= 767;

                jobTitleElement.textContent = message;
                jobTitleElement.style.color = '#dc3545';
                jobTitleElement.style.fontSize = isMobile ? '14px' : '16px';
                jobTitleElement.style.textAlign = 'center';
                jobTitleElement.style.fontWeight = '500';
            }

            function handleFileUpload(input) {
                const fileUploadText = document.getElementById('file-upload-text');
                const fileUploadDisplay = document.getElementById('file-upload-display');

                console.log('File upload triggered:', input.files);

                if (input.files && input.files[0]) {
                    const file = input.files[0];
                    const fileName = file.name;
                    const fileSize = file.size;
                    const maxSize = 5 * 1024 * 1024; // 5MB in bytes

                    console.log('File details:', {
                        name: fileName,
                        size: fileSize,
                        type: file.type
                    });

                    // Check file size with mobile-friendly messaging
                    if (fileSize > maxSize) {
                        const isMobile = window.innerWidth <= 767;
                        const message = isMobile ?
                            'File too large (max 5MB). Please choose a smaller file.' :
                            'File size exceeds 5MB limit. Please choose a smaller file.';
                        alert(message);
                        input.value = '';
                        fileUploadText.textContent = 'Click to upload or drag and drop';
                        fileUploadDisplay.style.borderColor = '#ddd';
                        return;
                    }

                    // Check file type - be more lenient with file type checking
                    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                    const fileExtension = fileName.toLowerCase().split('.').pop();
                    const allowedExtensions = ['pdf', 'doc', 'docx'];

                    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
                        const isMobile = window.innerWidth <= 767;
                        const message = isMobile ?
                            'Please upload PDF, DOC, or DOCX only.' :
                            'Please upload a PDF, DOC, or DOCX file.';
                        alert(message);
                        input.value = '';
                        fileUploadText.textContent = 'Click to upload or drag and drop';
                        fileUploadDisplay.style.borderColor = '#ddd';
                        return;
                    }

                    // Update display
                    fileUploadText.textContent = fileName;
                    fileUploadDisplay.style.borderColor = '#007bff';
                    console.log('File upload successful:', fileName);
                } else {
                    fileUploadText.textContent = 'Click to upload or drag and drop';
                    fileUploadDisplay.style.borderColor = '#ddd';
                    console.log('No file selected');
                }
            }

            // Function to submit form when button is clicked
            async function submitApplicationForm() {
                const form = document.getElementById('job-application-form');
                submitApplication(form);
            }

            // Handle form submission
            async function submitApplication(formElement) {
                if (!currentJob) {
                    alert('Job information not loaded. Please refresh the page and try again.');
                    return;
                }

                // Check form validation
                const $form = $(formElement);
                $form.validator('validate');

                // Wait a moment for validation to complete
                setTimeout(function() {
                    if ($form.find('.has-error').length > 0) {
                        // Form has validation errors, don't submit
                        return;
                    }

                    // Form is valid, proceed with submission
                    proceedWithSubmission(formElement);
                }, 100);
            }

            // Proceed with actual form submission after validation
            async function proceedWithSubmission(formElement) {
                const formData = new FormData(formElement);

                // Get values directly from FormData
                const name = formData.get('name') ? formData.get('name').trim() : '';
                const email = formData.get('email') ? formData.get('email').trim() : '';
                const linkedinLink = formData.get('linkedin-link') ? formData.get('linkedin-link').trim() : '';
                const cvFile = formData.get('cv-upload');

                // Debug logging
                console.log('FormData entries:');
                for (let [key, value] of formData.entries()) {
                    console.log(key, value);
                }

                console.log('Form validation:', {
                    name: name,
                    email: email,
                    cvFile: cvFile,
                    hasName: !!name,
                    hasEmail: !!email,
                    hasCvFile: !!cvFile && cvFile.size > 0
                });

                if (!name || !email || !cvFile || cvFile.size === 0) {
                    const isMobile = window.innerWidth <= 767;
                    const missing = [];
                    if (!name) missing.push('Name');
                    if (!email) missing.push('Email');
                    if (!cvFile || cvFile.size === 0) missing.push('CV file');

                    const message = isMobile ?
                        `Please complete: ${missing.join(', ')}` :
                        `Please fill in all required fields and upload your CV.\nMissing: ${missing.join(', ')}`;
                    alert(message);
                    return;
                }

                // Add job-specific data to FormData
                formData.append('jobId', currentJob.id);
                formData.append('jobTitle', currentJob.title);
                formData.append('jobDescription', currentJob.description);

                // Add LinkedIn link with correct field name for backend
                if (linkedinLink) {
                    formData.append('linkedinLink', linkedinLink);
                }

                // Rename the file field to match backend expectation
                formData.delete('cv-upload');
                formData.append('cv', cvFile);

                // Show loading state
                const submitButton = document.getElementById('submit-application');
                const submitText = document.getElementById('submit-text');
                const submitSpinner = document.getElementById('submit-spinner');

                submitButton.disabled = true;
                submitText.textContent = 'Submitting...';
                submitSpinner.style.display = 'inline-block';

                try {
                    const response = await fetch(APPLICATION_API_URL, {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (response.ok) {
                        const isMobile = window.innerWidth <= 767;
                        const message = isMobile ?
                            'Application submitted! We\'ll contact you soon.' :
                            'Application submitted successfully! We will contact you soon.';
                        alert(message);
                        // Reset form
                        document.getElementById('job-application-form').reset();
                        document.getElementById('file-upload-text').textContent = 'Click to upload or drag and drop';
                        document.getElementById('file-upload-display').style.borderColor = '#ddd';
                    } else {
                        throw new Error(result.error || 'Failed to submit application');
                    }
                } catch (error) {
                    console.error('Error submitting application:', error);
                    const isMobile = window.innerWidth <= 767;
                    const message = isMobile ?
                        'Submission failed. Please try again.' :
                        'Failed to submit application. Please try again later.';
                    alert(message);
                } finally {
                    // Reset button state
                    submitButton.disabled = false;
                    submitText.textContent = 'Submit application';
                    submitSpinner.style.display = 'none';
                }
            }

            // Add drag and drop functionality
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize form validation
                $('#job-application-form').validator();

                // Load job details when page loads
                loadJobDetails();

                // Handle window resize for responsive adjustments
                let resizeTimeout;
                window.addEventListener('resize', function() {
                    clearTimeout(resizeTimeout);
                    resizeTimeout = setTimeout(function() {
                        // Reload job info to adjust responsive styling
                        if (currentJob) {
                            displayJobInfo(currentJob);
                        }
                    }, 250);
                });

                // Add form submission handler for traditional form submit (Enter key, etc.)
                document.getElementById('job-application-form').addEventListener('submit', function(event) {
                    event.preventDefault();
                    submitApplication(event.target);
                });

                // Handle form validation events
                $('#job-application-form').on('invalid.bs.validator', function() {
                    // Form has validation errors
                    console.log('Form validation failed');
                });

                $('#job-application-form').on('valid.bs.validator', function() {
                    // Form is valid
                    console.log('Form validation passed');
                });

                const fileUploadDisplay = document.getElementById('file-upload-display');
                const fileInput = document.getElementById('cv-upload');

                // Prevent default drag behaviors
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    fileUploadDisplay.addEventListener(eventName, preventDefaults, false);
                    document.body.addEventListener(eventName, preventDefaults, false);
                });

                // Highlight drop area when item is dragged over it
                ['dragenter', 'dragover'].forEach(eventName => {
                    fileUploadDisplay.addEventListener(eventName, highlight, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    fileUploadDisplay.addEventListener(eventName, unhighlight, false);
                });

                // Handle dropped files
                fileUploadDisplay.addEventListener('drop', handleDrop, false);

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                function highlight(e) {
                    fileUploadDisplay.style.borderColor = '#007bff';
                    fileUploadDisplay.style.backgroundColor = '#f8f9fa';
                }

                function unhighlight(e) {
                    fileUploadDisplay.style.borderColor = '#ddd';
                    fileUploadDisplay.style.backgroundColor = '#fff';
                }

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;

                    if (files.length > 0) {
                        fileInput.files = files;
                        handleFileUpload(fileInput);
                    }
                }
            });
        </script>

    </body>
</html>
